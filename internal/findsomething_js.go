package internal

import (
	"io/ioutil"
	"path/filepath"
	"strings"
)

// GetFindSomethingJS 从FindSomething项目中读取JavaScript代码
// 这样可以确保始终使用最新的FindSomething代码
func GetFindSomethingJS() (string, error) {
	// 读取FindSomething项目的background.js文件
	jsFilePath := filepath.Join("FindSomething", "background.js")
	content, err := ioutil.ReadFile(jsFilePath)
	if err != nil {
		return "", err
	}

	// 提取需要的函数和变量
	jsCode := string(content)

	// 提取核心函数和变量
	extractedCode := extractCoreJSFunctions(jsCode)

	return extractedCode, nil
}

// extractCoreJSFunctions 提取核心的JavaScript函数
func extractCoreJSFunctions(fullCode string) string {
	// 构建需要的JavaScript代码
	var coreCode strings.Builder

	// 添加nuclei正则表达式数组
	if start := strings.Index(fullCode, "var nuclei_regex = ["); start != -1 {
		if end := strings.Index(fullCode[start:], "];"); end != -1 {
			nucleiRegexCode := fullCode[start : start+end+2]
			coreCode.WriteString(nucleiRegexCode)
			coreCode.WriteString("\n\n")
		}
	}

	// 添加add函数
	if start := strings.Index(fullCode, "function add("); start != -1 {
		if end := findFunctionEnd(fullCode, start); end != -1 {
			addFunctionCode := fullCode[start:end]
			coreCode.WriteString(addFunctionCode)
			coreCode.WriteString("\n\n")
		}
	}

	// 添加get_secret函数
	if start := strings.Index(fullCode, "function get_secret("); start != -1 {
		if end := findFunctionEnd(fullCode, start); end != -1 {
			getSecretCode := fullCode[start:end]
			coreCode.WriteString(getSecretCode)
			coreCode.WriteString("\n\n")
		}
	}

	// 添加extract_info函数
	if start := strings.Index(fullCode, "function extract_info("); start != -1 {
		if end := findFunctionEnd(fullCode, start); end != -1 {
			extractInfoCode := fullCode[start:end]
			coreCode.WriteString(extractInfoCode)
			coreCode.WriteString("\n\n")
		}
	}

	return coreCode.String()
}

// findFunctionEnd 找到函数的结束位置
func findFunctionEnd(code string, start int) int {
	braceCount := 0
	inFunction := false

	for i := start; i < len(code); i++ {
		char := code[i]

		if char == '{' {
			braceCount++
			inFunction = true
		} else if char == '}' {
			braceCount--
			if inFunction && braceCount == 0 {
				return i + 1
			}
		}
	}

	return -1
}
